{"global_stats": {"mean": 0.5001410841941833, "std": 5.960464477539063e-08, "median": 0.5001410245895386, "q25": 0.5001410245895386, "q75": 0.5001410245895386, "q90": 0.5001410245895386, "q95": 0.5001410245895386, "q99": 0.5001410245895386, "q99_5": 0.5001410245895386, "q99_9": 0.5001410245895386, "min": 0.5001410245895386, "max": 0.5001410245895386, "count": 10800}, "threshold_info": {"threshold": 0.5001410245895386, "confidence": 0.995, "threshold_method": "quantile_percentile", "percentile": 99.5}, "model_type": "gan_discriminator"}